/* eslint-disable @typescript-eslint/no-explicit-any */
import { PiGearSixFill } from "react-icons/pi";
import { RiCheckboxCircleFill } from "react-icons/ri";

import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
// @ts-expect-error not needed
import { MODELS } from "./../../../utils/providers";
import { Button } from "../ui/button";
import { useState } from "react";
import ModelsInfo from "../models-info/models-info";

function Settings({
  open,
  onClose,
  model,
  error,
  onModelChange,
}: {
  open: boolean;
  model: string;
  error?: string;
  onClose: React.Dispatch<React.SetStateAction<boolean>>;
  onModelChange: (model: string) => void;
}) {
  const [showModelsInfo, setShowModelsInfo] = useState(false);

  return (
    <div className="">
      <Popover open={open} onOpenChange={onClose}>
        <PopoverTrigger asChild>
          <Button variant="gray" size="sm" className="bg-gradient-to-r from-neutral-800 to-neutral-700 hover:from-neutral-700 hover:to-neutral-600 border-neutral-600 text-neutral-200 shadow-lg transition-all duration-200">
            <PiGearSixFill className="size-4 mr-2" />
            <span className="font-medium">AI Settings</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="p-0 !w-[600px] max-h-[500px] overflow-hidden !bg-neutral-900 border-neutral-700 shadow-2xl"
          align="center"
        >
          <header className="flex items-center justify-between text-sm px-4 py-3 border-b bg-neutral-950 border-neutral-800 font-semibold text-neutral-200">
            <span>Choose AI Model</span>
            <span className="text-xs bg-blue-500/20 text-blue-400 rounded-full px-2 py-1">
              12 Available
            </span>
          </header>
          <main className="p-4 max-h-[400px] overflow-y-auto">
            {error !== "" && (
              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 mb-4">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            {/* Models Grid */}
            <div className="space-y-4">
              {/* DeepSeek Models */}
              <div>
                <h3 className="text-sm font-semibold text-neutral-300 mb-3 flex items-center gap-2">
                  🚀 DeepSeek Models
                  <span className="text-xs bg-red-500/20 text-red-400 rounded-full px-2 py-0.5">Direct API</span>
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {MODELS.filter((m: any) => m.autoProvider === 'deepseek').map(
                    ({
                      value,
                      label,
                      description,
                      isThinker = false,
                    }: {
                      value: string;
                      label: string;
                      description: string;
                      isThinker?: boolean;
                    }) => (
                      <button
                        key={value}
                        onClick={() => onModelChange(value)}
                        className={`p-3 rounded-lg border text-left transition-all duration-200 hover:scale-105 ${
                          model === value
                            ? 'bg-blue-500/20 border-blue-500 shadow-lg shadow-blue-500/20'
                            : 'bg-neutral-800/50 border-neutral-700 hover:bg-neutral-800 hover:border-neutral-600'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-neutral-200 text-sm">{label}</span>
                          {isThinker && (
                            <span className="text-xs bg-purple-500 text-white rounded-full px-1.5 py-0.5">
                              🧠
                            </span>
                          )}
                          {model === value && (
                            <RiCheckboxCircleFill className="text-blue-500 ml-auto" />
                          )}
                        </div>
                        <p className="text-xs text-neutral-400 leading-relaxed">{description}</p>
                      </button>
                    )
                  )}
                </div>
              </div>

              {/* Google Gemini Models */}
              <div>
                <h3 className="text-sm font-semibold text-neutral-300 mb-3 flex items-center gap-2">
                  🔮 Google Gemini
                  <span className="text-xs bg-blue-500/20 text-blue-400 rounded-full px-2 py-0.5">Direct API</span>
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {MODELS.filter((m: any) => m.autoProvider === 'google').map(
                    ({
                      value,
                      label,
                      description,
                      isThinker = false,
                      isExperimental = false,
                    }: {
                      value: string;
                      label: string;
                      description: string;
                      isThinker?: boolean;
                      isExperimental?: boolean;
                    }) => (
                      <button
                        key={value}
                        onClick={() => onModelChange(value)}
                        className={`p-3 rounded-lg border text-left transition-all duration-200 hover:scale-105 ${
                          model === value
                            ? 'bg-blue-500/20 border-blue-500 shadow-lg shadow-blue-500/20'
                            : 'bg-neutral-800/50 border-neutral-700 hover:bg-neutral-800 hover:border-neutral-600'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-neutral-200 text-sm">{label}</span>
                          {isExperimental && (
                            <span className="text-xs bg-orange-500 text-white rounded-full px-1.5 py-0.5">
                              🧪
                            </span>
                          )}
                          {isThinker && (
                            <span className="text-xs bg-purple-500 text-white rounded-full px-1.5 py-0.5">
                              🧠
                            </span>
                          )}
                          {model === value && (
                            <RiCheckboxCircleFill className="text-blue-500 ml-auto" />
                          )}
                        </div>
                        <p className="text-xs text-neutral-400 leading-relaxed">{description}</p>
                      </button>
                    )
                  )}
                </div>
              </div>

              {/* OpenAI Models */}
              <div>
                <h3 className="text-sm font-semibold text-neutral-300 mb-3 flex items-center gap-2">
                  🤖 OpenAI
                  <span className="text-xs bg-green-500/20 text-green-400 rounded-full px-2 py-0.5">Direct API</span>
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {MODELS.filter((m: any) => m.autoProvider === 'openai').map(
                    ({
                      value,
                      label,
                      description,
                      isThinker = false,
                    }: {
                      value: string;
                      label: string;
                      description: string;
                      isThinker?: boolean;
                    }) => (
                      <button
                        key={value}
                        onClick={() => onModelChange(value)}
                        className={`p-3 rounded-lg border text-left transition-all duration-200 hover:scale-105 ${
                          model === value
                            ? 'bg-blue-500/20 border-blue-500 shadow-lg shadow-blue-500/20'
                            : 'bg-neutral-800/50 border-neutral-700 hover:bg-neutral-800 hover:border-neutral-600'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-neutral-200 text-sm">{label}</span>
                          {isThinker && (
                            <span className="text-xs bg-purple-500 text-white rounded-full px-1.5 py-0.5">
                              🧠
                            </span>
                          )}
                          {model === value && (
                            <RiCheckboxCircleFill className="text-blue-500 ml-auto" />
                          )}
                        </div>
                        <p className="text-xs text-neutral-400 leading-relaxed">{description}</p>
                      </button>
                    )
                  )}
                </div>
              </div>

              {/* Anthropic Models */}
              <div>
                <h3 className="text-sm font-semibold text-neutral-300 mb-3 flex items-center gap-2">
                  🧠 Anthropic Claude
                  <span className="text-xs bg-purple-500/20 text-purple-400 rounded-full px-2 py-0.5">Direct API</span>
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {MODELS.filter((m: any) => m.autoProvider === 'anthropic').map(
                    ({
                      value,
                      label,
                      description,
                      isThinker = false,
                    }: {
                      value: string;
                      label: string;
                      description: string;
                      isThinker?: boolean;
                    }) => (
                      <button
                        key={value}
                        onClick={() => onModelChange(value)}
                        className={`p-3 rounded-lg border text-left transition-all duration-200 hover:scale-105 ${
                          model === value
                            ? 'bg-blue-500/20 border-blue-500 shadow-lg shadow-blue-500/20'
                            : 'bg-neutral-800/50 border-neutral-700 hover:bg-neutral-800 hover:border-neutral-600'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-neutral-200 text-sm">{label}</span>
                          {isThinker && (
                            <span className="text-xs bg-purple-500 text-white rounded-full px-1.5 py-0.5">
                              🧠
                            </span>
                          )}
                          {model === value && (
                            <RiCheckboxCircleFill className="text-blue-500 ml-auto" />
                          )}
                        </div>
                        <p className="text-xs text-neutral-400 leading-relaxed">{description}</p>
                      </button>
                    )
                  )}
                </div>
              </div>

              {/* Meta Models */}
              <div>
                <h3 className="text-sm font-semibold text-neutral-300 mb-3 flex items-center gap-2">
                  🦙 Meta Llama
                  <span className="text-xs bg-blue-500/20 text-blue-400 rounded-full px-2 py-0.5">Direct API</span>
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {MODELS.filter((m: any) => m.autoProvider === 'meta').map(
                    ({
                      value,
                      label,
                      description,
                    }: {
                      value: string;
                      label: string;
                      description: string;
                    }) => (
                      <button
                        key={value}
                        onClick={() => onModelChange(value)}
                        className={`p-3 rounded-lg border text-left transition-all duration-200 hover:scale-105 ${
                          model === value
                            ? 'bg-blue-500/20 border-blue-500 shadow-lg shadow-blue-500/20'
                            : 'bg-neutral-800/50 border-neutral-700 hover:bg-neutral-800 hover:border-neutral-600'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-neutral-200 text-sm">{label}</span>
                          <span className="text-xs bg-blue-500 text-white rounded-full px-1.5 py-0.5">
                            🎨
                          </span>
                          {model === value && (
                            <RiCheckboxCircleFill className="text-blue-500 ml-auto" />
                          )}
                        </div>
                        <p className="text-xs text-neutral-400 leading-relaxed">{description}</p>
                      </button>
                    )
                  )}
                </div>
              </div>

              {/* Alibaba Models */}
              <div>
                <h3 className="text-sm font-semibold text-neutral-300 mb-3 flex items-center gap-2">
                  💻 Alibaba Qwen
                  <span className="text-xs bg-yellow-500/20 text-yellow-400 rounded-full px-2 py-0.5">Direct API</span>
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {MODELS.filter((m: any) => m.autoProvider === 'alibaba').map(
                    ({
                      value,
                      label,
                      description,
                    }: {
                      value: string;
                      label: string;
                      description: string;
                    }) => (
                      <button
                        key={value}
                        onClick={() => onModelChange(value)}
                        className={`p-3 rounded-lg border text-left transition-all duration-200 hover:scale-105 ${
                          model === value
                            ? 'bg-blue-500/20 border-blue-500 shadow-lg shadow-blue-500/20'
                            : 'bg-neutral-800/50 border-neutral-700 hover:bg-neutral-800 hover:border-neutral-600'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-neutral-200 text-sm">{label}</span>
                          <span className="text-xs bg-green-500 text-white rounded-full px-1.5 py-0.5">
                            💻
                          </span>
                          {model === value && (
                            <RiCheckboxCircleFill className="text-blue-500 ml-auto" />
                          )}
                        </div>
                        <p className="text-xs text-neutral-400 leading-relaxed">{description}</p>
                      </button>
                    )
                  )}
                </div>
              </div>
            </div>

          </main>
        </PopoverContent>
      </Popover>

      <ModelsInfo
        open={showModelsInfo}
        onClose={() => setShowModelsInfo(false)}
      />
    </div>
  );
}
export default Settings;
