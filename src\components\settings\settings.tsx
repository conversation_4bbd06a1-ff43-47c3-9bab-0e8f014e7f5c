/* eslint-disable @typescript-eslint/no-explicit-any */
import classNames from "classnames";
import { PiGearSixFill } from "react-icons/pi";
import { RiCheckboxCircleFill } from "react-icons/ri";
import { Info } from "lucide-react";

import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
// @ts-expect-error not needed
import { PROVIDERS, MODELS } from "./../../../utils/providers";
import { Button } from "../ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { useMemo, useState } from "react";
import { useUpdateEffect } from "react-use";
import ModelsInfo from "../models-info/models-info";

function Settings({
  open,
  onClose,
  provider,
  model,
  error,
  onChange,
  onModelChange,
}: {
  open: boolean;
  provider: string;
  model: string;
  error?: string;
  onClose: React.Dispatch<React.SetStateAction<boolean>>;
  onChange: (provider: string) => void;
  onModelChange: (model: string) => void;
}) {
  const [showModelsInfo, setShowModelsInfo] = useState(false);

  const modelAvailableProviders = useMemo(() => {
    const availableProviders = MODELS.find(
      (m: { value: string }) => m.value === model
    )?.providers;
    if (!availableProviders) return Object.keys(PROVIDERS);
    return Object.keys(PROVIDERS).filter((id) =>
      availableProviders.includes(id)
    );
  }, [model]);

  useUpdateEffect(() => {
    if (provider !== "auto" && !modelAvailableProviders.includes(provider)) {
      onChange("auto");
    }
  }, [model, provider]);

  return (
    <div className="">
      <Popover open={open} onOpenChange={onClose}>
        <PopoverTrigger asChild>
          <Button variant="gray" size="sm" className="bg-gradient-to-r from-neutral-800 to-neutral-700 hover:from-neutral-700 hover:to-neutral-600 border-neutral-600 text-neutral-200 shadow-lg transition-all duration-200">
            <PiGearSixFill className="size-4 mr-2" />
            <span className="font-medium">AI Settings</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="p-0 !w-[480px] overflow-hidden !bg-gradient-to-br from-neutral-900 to-neutral-950 border-neutral-700 shadow-2xl"
          align="center"
        >
          <header className="flex items-center text-sm px-6 py-4 border-b gap-3 bg-gradient-to-r from-neutral-950 to-neutral-900 border-neutral-700 font-semibold text-neutral-100">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-base font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                AI Model Settings
              </span>
            </div>
            <div className="ml-auto flex items-center gap-2">
              <span className="text-xs bg-green-500/20 text-green-400 rounded-full px-2 py-1 border border-green-500/30">
                12 Models Available
              </span>
            </div>
          </header>
          <main className="px-6 pt-6 pb-8 space-y-6">
            {/* Quick Info Card */}
            <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-4">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm font-bold">AI</span>
                </div>
                <div>
                  <h3 className="text-neutral-200 font-semibold text-sm">Direct API Integration</h3>
                  <p className="text-neutral-400 text-xs">No login required • Unlimited usage</p>
                </div>
              </div>
            </div>

            {error !== "" && (
              <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <p className="text-red-400 text-sm font-medium">{error}</p>
                </div>
              </div>
            )}

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-neutral-200 font-semibold text-base mb-1">Choose AI Model</h3>
                  <p className="text-neutral-400 text-sm">Select the best model for your task</p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowModelsInfo(true)}
                  className="text-xs text-blue-400 hover:text-blue-300 hover:bg-blue-500/10 border border-blue-500/20"
                >
                  <Info className="size-3 mr-1" />
                  View Details
                </Button>
              </div>
              <div className="space-y-3">
                <Select defaultValue={model} onValueChange={onModelChange}>
                  <SelectTrigger className="w-full h-12 bg-neutral-800/50 border-neutral-600 hover:border-neutral-500 focus:border-blue-500 transition-colors">
                    <SelectValue placeholder="🤖 Select an AI model" />
                  </SelectTrigger>
                  <SelectContent className="bg-neutral-900 border-neutral-700 max-h-[400px]">
                    {/* DeepSeek Models */}
                    <SelectGroup>
                      <SelectLabel className="text-neutral-300 font-semibold text-sm py-2 px-3 bg-gradient-to-r from-red-500/10 to-orange-500/10 border-l-4 border-red-500">
                        🚀 DeepSeek (Direct API)
                      </SelectLabel>
                      {MODELS.filter((m: any) => m.autoProvider === 'deepseek').map(
                        ({
                          value,
                          label,
                          description,
                          isThinker = false,
                        }: {
                          value: string;
                          label: string;
                          description: string;
                          isThinker?: boolean;
                        }) => (
                          <SelectItem key={value} value={value} className="py-3 px-4 hover:bg-neutral-800/50 focus:bg-neutral-800/50">
                            <div className="flex flex-col items-start w-full">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-neutral-200">{label}</span>
                                {isThinker && (
                                  <span className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full px-2 py-0.5 font-medium">
                                    🧠 Thinker
                                  </span>
                                )}
                              </div>
                              <span className="text-xs text-neutral-400 leading-relaxed">{description}</span>
                            </div>
                          </SelectItem>
                        )
                      )}
                    </SelectGroup>

                    {/* Google Models */}
                    <SelectGroup>
                      <SelectLabel className="text-neutral-300 font-semibold text-sm py-2 px-3 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border-l-4 border-blue-500">
                        🔮 Google Gemini (Direct API)
                      </SelectLabel>
                      {MODELS.filter((m: any) => m.autoProvider === 'google').map(
                        ({
                          value,
                          label,
                          description,
                          isThinker = false,
                          isExperimental = false,
                        }: {
                          value: string;
                          label: string;
                          description: string;
                          isThinker?: boolean;
                          isExperimental?: boolean;
                        }) => (
                          <SelectItem key={value} value={value} className="py-3 px-4 hover:bg-neutral-800/50 focus:bg-neutral-800/50">
                            <div className="flex flex-col items-start w-full">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-neutral-200">{label}</span>
                                {isExperimental && (
                                  <span className="text-xs bg-gradient-to-r from-orange-500 to-yellow-500 text-white rounded-full px-2 py-0.5 font-medium">
                                    🧪 Experimental
                                  </span>
                                )}
                                {isThinker && (
                                  <span className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full px-2 py-0.5 font-medium">
                                    🧠 Thinker
                                  </span>
                                )}
                              </div>
                              <span className="text-xs text-neutral-400 leading-relaxed">{description}</span>
                            </div>
                          </SelectItem>
                        )
                      )}
                    </SelectGroup>

                    {/* OpenAI Models */}
                    <SelectGroup>
                      <SelectLabel className="text-neutral-300 font-semibold text-sm py-2 px-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border-l-4 border-green-500">
                        🤖 OpenAI (Direct API)
                      </SelectLabel>
                      {MODELS.filter((m: any) => m.autoProvider === 'openai').map(
                        ({
                          value,
                          label,
                          description,
                          isThinker = false,
                        }: {
                          value: string;
                          label: string;
                          description: string;
                          isThinker?: boolean;
                        }) => (
                          <SelectItem key={value} value={value} className="py-3 px-4 hover:bg-neutral-800/50 focus:bg-neutral-800/50">
                            <div className="flex flex-col items-start w-full">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-neutral-200">{label}</span>
                                {isThinker && (
                                  <span className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full px-2 py-0.5 font-medium">
                                    🧠 Thinker
                                  </span>
                                )}
                              </div>
                              <span className="text-xs text-neutral-400 leading-relaxed">{description}</span>
                            </div>
                          </SelectItem>
                        )
                      )}
                    </SelectGroup>

                    {/* Anthropic Models */}
                    <SelectGroup>
                      <SelectLabel className="text-neutral-300 font-semibold text-sm py-2 px-3 bg-gradient-to-r from-purple-500/10 to-indigo-500/10 border-l-4 border-purple-500">
                        🧠 Anthropic Claude (Direct API)
                      </SelectLabel>
                      {MODELS.filter((m: any) => m.autoProvider === 'anthropic').map(
                        ({
                          value,
                          label,
                          description,
                          isThinker = false,
                        }: {
                          value: string;
                          label: string;
                          description: string;
                          isThinker?: boolean;
                        }) => (
                          <SelectItem key={value} value={value} className="py-3 px-4 hover:bg-neutral-800/50 focus:bg-neutral-800/50">
                            <div className="flex flex-col items-start w-full">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-neutral-200">{label}</span>
                                {isThinker && (
                                  <span className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full px-2 py-0.5 font-medium">
                                    🧠 Thinker
                                  </span>
                                )}
                              </div>
                              <span className="text-xs text-neutral-400 leading-relaxed">{description}</span>
                            </div>
                          </SelectItem>
                        )
                      )}
                    </SelectGroup>

                    {/* Meta Models */}
                    <SelectGroup>
                      <SelectLabel className="text-neutral-300 font-semibold text-sm py-2 px-3 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border-l-4 border-blue-500">
                        🦙 Meta Llama (Direct API)
                      </SelectLabel>
                      {MODELS.filter((m: any) => m.autoProvider === 'meta').map(
                        ({
                          value,
                          label,
                          description,
                        }: {
                          value: string;
                          label: string;
                          description: string;
                        }) => (
                          <SelectItem key={value} value={value} className="py-3 px-4 hover:bg-neutral-800/50 focus:bg-neutral-800/50">
                            <div className="flex flex-col items-start w-full">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-neutral-200">{label}</span>
                                <span className="text-xs bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-2 py-0.5 font-medium">
                                  🎨 Creative
                                </span>
                              </div>
                              <span className="text-xs text-neutral-400 leading-relaxed">{description}</span>
                            </div>
                          </SelectItem>
                        )
                      )}
                    </SelectGroup>

                    {/* Alibaba Models */}
                    <SelectGroup>
                      <SelectLabel className="text-neutral-300 font-semibold text-sm py-2 px-3 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border-l-4 border-yellow-500">
                        💻 Alibaba Qwen (Direct API)
                      </SelectLabel>
                      {MODELS.filter((m: any) => m.autoProvider === 'alibaba').map(
                        ({
                          value,
                          label,
                          description,
                        }: {
                          value: string;
                          label: string;
                          description: string;
                        }) => (
                          <SelectItem key={value} value={value} className="py-3 px-4 hover:bg-neutral-800/50 focus:bg-neutral-800/50">
                            <div className="flex flex-col items-start w-full">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-neutral-200">{label}</span>
                                <span className="text-xs bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full px-2 py-0.5 font-medium">
                                  💻 Coding
                                </span>
                              </div>
                              <span className="text-xs text-neutral-400 leading-relaxed">{description}</span>
                            </div>
                          </SelectItem>
                        )
                      )}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            <div className="space-y-6">
              {/* Auto Provider Toggle */}
              <div className="bg-gradient-to-r from-neutral-800/30 to-neutral-700/30 border border-neutral-600/50 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <h4 className="text-neutral-200 font-semibold text-sm">Smart Auto-Selection</h4>
                    </div>
                    <p className="text-xs text-neutral-400 leading-relaxed">
                      AI will automatically choose the best provider based on your prompt and task complexity
                    </p>
                  </div>
                  <div
                    className={classNames(
                      "bg-neutral-600 rounded-full min-w-12 w-12 h-7 flex items-center p-1 cursor-pointer transition-all duration-300 shadow-inner",
                      {
                        "!bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg": provider === "auto",
                      }
                    )}
                    onClick={() => {
                      const foundModel = MODELS.find(
                        (m: { value: string }) => m.value === model
                      );
                      if (provider === "auto") {
                        onChange(foundModel.autoProvider);
                      } else {
                        onChange("auto");
                      }
                    }}
                  >
                    <div
                      className={classNames(
                        "w-5 h-5 rounded-full shadow-md transition-all duration-300 bg-white",
                        {
                          "translate-x-5": provider === "auto",
                        }
                      )}
                    />
                  </div>
                </div>
              </div>
              {/* Manual Provider Selection */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <h4 className="text-neutral-200 font-semibold text-sm">Manual Provider Selection</h4>
                  <span className="text-xs bg-neutral-600 text-neutral-300 rounded-full px-2 py-1">
                    Advanced
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-3">
                  {modelAvailableProviders.map((id: string) => (
                    <Button
                      key={id}
                      variant={id === provider ? "default" : "secondary"}
                      size="sm"
                      onClick={() => {
                        onChange(id);
                      }}
                      className={classNames(
                        "h-12 flex flex-col items-center justify-center gap-1 transition-all duration-200",
                        {
                          "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg border-blue-500": id === provider,
                          "hover:bg-neutral-700 hover:border-neutral-600": id !== provider,
                        }
                      )}
                    >
                      <div className="flex items-center gap-2">
                        <img
                          src={`/providers/${id}.svg`}
                          alt={PROVIDERS[id].name}
                          className="size-4"
                        />
                        <span className="text-xs font-medium">{PROVIDERS[id].name}</span>
                        {id === provider && (
                          <RiCheckboxCircleFill className="size-3 text-white" />
                        )}
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gradient-to-r from-neutral-800/50 to-neutral-700/50 border-t border-neutral-600/50 px-4 py-3 mt-6 -mx-6 -mb-8">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-2 text-neutral-400">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Direct API • No limits</span>
                </div>
                <div className="text-neutral-500">
                  WIDDX DEV AI
                </div>
              </div>
            </div>
          </main>
        </PopoverContent>
      </Popover>

      <ModelsInfo
        open={showModelsInfo}
        onClose={() => setShowModelsInfo(false)}
      />
    </div>
  );
}
export default Settings;
