// Test that the system works without login requirement
import dotenv from 'dotenv';

dotenv.config();

async function testNoLoginRequired() {
  console.log('🔓 Testing WIDDX DEV without login requirement...\n');
  
  // Test API endpoint
  const testData = {
    model: 'gemini-1.5-flash',
    provider: 'google',
    prompt: 'Create a simple hello world HTML page'
  };
  
  try {
    console.log('📡 Testing /api/ask-ai endpoint...');
    
    const response = await fetch('http://localhost:5173/api/ask-ai', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });
    
    console.log('📊 Response status:', response.status);
    console.log('📋 Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      console.log('✅ SUCCESS: API works without login!');
      
      // Read the streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let result = '';
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        result += chunk;
      }
      
      console.log('📝 Response length:', result.length);
      console.log('📄 First 200 characters:', result.substring(0, 200) + '...');
      
    } else {
      const errorData = await response.text();
      console.log('❌ FAILED: Response not OK');
      console.log('📄 Error response:', errorData);
    }
    
  } catch (error) {
    console.error('❌ FAILED: Network error:', error.message);
  }
}

async function testAPIKeysStatus() {
  console.log('\n🔑 Checking API Keys status:');
  
  const apiKeys = {
    'Google (Gemini)': process.env.GOOGLE_AI_API_KEY,
    'DeepSeek': process.env.DEEPSEEK_API_KEY,
    'OpenAI': process.env.OPENAI_API_KEY,
    'Anthropic': process.env.ANTHROPIC_API_KEY,
    'Meta': process.env.META_API_KEY,
    'Alibaba': process.env.ALIBABA_API_KEY,
    'HF Token': process.env.HF_TOKEN
  };
  
  Object.entries(apiKeys).forEach(([provider, key]) => {
    const status = key ? '✅ Configured' : '❌ Missing';
    const preview = key ? `${key.substring(0, 10)}...` : 'Not set';
    console.log(`${status} ${provider}: ${preview}`);
  });
}

// Run tests
async function runTests() {
  await testAPIKeysStatus();
  await testNoLoginRequired();
  
  console.log('\n🎯 Test Summary:');
  console.log('- API Keys: Configured for direct usage');
  console.log('- Login Requirement: Bypassed for direct APIs');
  console.log('- Rate Limiting: Increased for local usage');
  console.log('\n✅ WIDDX DEV should now work without login!');
}

runTests().catch(console.error);
